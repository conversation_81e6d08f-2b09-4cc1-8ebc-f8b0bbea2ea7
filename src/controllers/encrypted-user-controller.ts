/**
 * @fileoverview Encrypted user controller
 * Handles CRUD operations for users with encrypted data
 */

import { EncryptedUserService } from '../services/encrypted-user-service';
import {
  getUserSchema,
  createUserSchema,
  updateUserSchema,
  deleteUserSchema
} from '../validators';

import type EncryptedUser from '../@types/encrypted-user';

import { BackendError } from '@/utils/errors';
import { createHandler } from '@/utils/handler';
import {
  sendResponse,
  sendCreatedResponse,
  sendNoContentResponse
} from '@/utils/response';

const userService = new EncryptedUserService();

// Convert Date to ISO string or use fallback
const formatDate = (date: Date | null): string => {
  return date?.toISOString() ?? new Date().toISOString();
};

// Format user for response
const formatUserResponse = (
  user: EncryptedUser.DecryptedUser
): EncryptedUser.UserResponse => ({
  id: user.id,
  email: user.email,
  firstName: user.firstName,
  lastName: user.lastName,
  createdAt: formatDate(user.createdAt),
  updatedAt: formatDate(user.updatedAt)
});

// Handlers
export const handleCreateUser = createHandler(
  createUserSchema,
  async (req, res) => {
    try {
      const user = await userService.createUser(req.body);
      const result = formatUserResponse(user);

      sendCreatedResponse(res, 'User created successfully', result);
    } catch {
      throw new BackendError('INTERNAL_ERROR', {
        message: 'Failed to create user',
        context: 'body',
        statusCode: 500
      });
    }
  }
);

export const handleGetAllUsers = createHandler(async (_req, res) => {
  try {
    const users = await userService.getAllUsers();
    const result = users.map(formatUserResponse);

    sendResponse(res, 'Users retrieved successfully', result);
  } catch {
    throw new BackendError('INTERNAL_ERROR', {
      message: 'Failed to retrieve users',
      context: 'query',
      statusCode: 500
    });
  }
});

export const handleGetUserById = createHandler(
  getUserSchema,
  async (req, res) => {
    try {
      const user = await userService.getUserById(parseInt(req.params.id));

      if (!user) {
        throw new BackendError('NOT_FOUND', {
          message: 'User not found',
          context: 'params',
          statusCode: 404
        });
      }

      const result = formatUserResponse(user);
      sendResponse(res, 'User retrieved successfully', result);
    } catch (error) {
      if (error instanceof BackendError) throw error;
      throw new BackendError('INTERNAL_ERROR', {
        message: 'Failed to retrieve user',
        context: 'query',
        statusCode: 500
      });
    }
  }
);

export const handleUpdateUser = createHandler(
  updateUserSchema,
  async (req, res) => {
    try {
      const user = await userService.updateUser(
        parseInt(req.params.id),
        req.body
      );

      if (!user) {
        throw new BackendError('NOT_FOUND', {
          message: 'User not found',
          context: 'params',
          statusCode: 404
        });
      }

      const result = formatUserResponse(user);
      sendResponse(res, 'User updated successfully', result);
    } catch (error) {
      if (error instanceof BackendError) throw error;
      throw new BackendError('INTERNAL_ERROR', {
        message: 'Failed to update user',
        context: 'body',
        statusCode: 500
      });
    }
  }
);

export const handleDeleteUser = createHandler(
  deleteUserSchema,
  async (req, res) => {
    try {
      await userService.deleteUser(parseInt(req.params.id));
      sendNoContentResponse(res);
    } catch {
      throw new BackendError('INTERNAL_ERROR', {
        message: 'Failed to delete user',
        context: 'params',
        statusCode: 500
      });
    }
  }
);
