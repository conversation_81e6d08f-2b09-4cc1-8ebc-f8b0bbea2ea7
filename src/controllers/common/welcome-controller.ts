import { z } from 'zod';

import { createHandler } from '@/utils/handler';
import { sendResponse } from '@/utils/response';

const welcomeSchema = {
  response: {
    body: z.object({
      message: z.string()
    })
  }
} as const;

export const handleWelcome = createHandler(welcomeSchema, async (req, res) => {
  const result = {
    message: `Hi i am here`
  };

  sendResponse(res, 'Welcome message retrieved successfully', result);
});
