/**
 * @fileoverview Authentication controller
 * Handles user authentication, account creation, and OTP verification
 */

import argon2 from 'argon2';
import jwt from 'jsonwebtoken';
import { z } from 'zod';

import { BackendError } from '@/utils/errors';
import { create<PERSON><PERSON><PERSON> } from '@/utils/handler';
import { sendResponse, sendCreatedResponse } from '@/utils/response';

// Login schemas - response body only defines the 'result' part
const loginSchema = {
  request: {
    body: z.object({
      email: z.string().email('Invalid email format'),
      password: z.string().min(8, 'Password must be at least 8 characters')
    })
  },
  response: {
    body: z.object({
      token: z.string(),
      user: z.object({
        id: z.string(),
        email: z.string(),
        name: z.string()
      })
    })
  }
} as const;

// Account creation schemas - response body only defines the 'result' part
const createAccountSchema = {
  request: {
    body: z.object({
      email: z.string().email('Invalid email format'),
      password: z.string().min(8, 'Password must be at least 8 characters'),
      name: z.string().min(2, 'Name must be at least 2 characters'),
      phone: z
        .string()
        .regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format')
    })
  },
  response: {
    body: z.object({
      userId: z.string()
    })
  }
} as const;

// OTP verification schemas - response body only defines the 'result' part
const verifyOtpSchema = {
  request: {
    body: z.object({
      userId: z.string().uuid('Invalid user ID'),
      otp: z.string().length(6, 'OTP must be 6 digits')
    })
  },
  response: {
    body: z.object({
      token: z.string(),
      user: z.object({
        id: z.string(),
        email: z.string(),
        name: z.string(),
        verified: z.boolean()
      })
    })
  }
} as const;

/**
 * Handles user login
 * Validates credentials and returns JWT token if valid
 */
export const handleLogin = createHandler(loginSchema, async (req, res) => {
  const { password } = req.body;

  // TODO: Replace with actual database query
  const user = {
    id: '123',
    email: '<EMAIL>',
    name: 'Test User',
    hashedPassword: await argon2.hash('password123')
  };

  // Verify password
  const isValid = await argon2.verify(user.hashedPassword, password);
  if (!isValid) {
    throw new BackendError('INVALID_PASSWORD', {
      message: 'Invalid email or password',
      context: 'body',
      statusCode: 401
    });
  }

  // Generate JWT token
  const token = jwt.sign(
    { userId: user.id, email: user.email },
    process.env.JWT_SECRET || 'your-secret-key',
    { expiresIn: '24h' }
  );

  const result = {
    token,
    user: {
      id: user.id,
      email: user.email,
      name: user.name
    }
  };

  sendResponse(res, 'Login successful', result);
});

/**
 * Handles new account creation
 * Creates user account and sends verification OTP
 */
export const handleCreateAccount = createHandler(
  createAccountSchema,
  async (req, res) => {
    const { password } = req.body;

    // Check if user already exists
    // TODO: Replace with actual database query
    const existingUser = false;
    if (existingUser) {
      throw new BackendError('CONFLICT', {
        message: 'User with this email already exists',
        context: 'body',
        statusCode: 409
      });
    }

    // Hash password
    const hashedPassword = await argon2.hash(password);

    // Create user
    // TODO: Replace with actual database query
    const userId = 'new-user-123';

    // Send OTP
    // TODO: Implement OTP sending logic
    console.log('Hashed password:', hashedPassword); // TODO: Remove this log

    const result = { userId };
    sendCreatedResponse(
      res,
      'Account created successfully. Please verify your email.',
      result
    );
  }
);

/**
 * Handles OTP verification
 * Verifies OTP and activates user account
 */
export const handleVerifyOtp = createHandler(
  verifyOtpSchema,
  async (req, res) => {
    const { userId, otp } = req.body;

    // TODO: Replace with actual OTP verification logic
    const isValidOtp = otp === '123456';
    if (!isValidOtp) {
      throw new BackendError('VALIDATION_ERROR', {
        message: 'Invalid or expired OTP',
        context: 'body',
        statusCode: 400
      });
    }

    // TODO: Replace with actual database query to mark user as verified
    const user = {
      id: userId,
      email: '<EMAIL>',
      name: 'Test User',
      verified: true
    };

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    const result = {
      token,
      user
    };

    sendResponse(res, 'OTP verified successfully', result);
  }
);
