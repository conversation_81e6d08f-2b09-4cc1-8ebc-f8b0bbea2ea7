/**
 * @fileoverview Standardized API response utilities
 * Provides type-safe response formatting for consistent API responses
 */

import type { Response } from 'express';

/**
 * Standard success response format
 */
export interface ApiResponse<T = any> {
  code: 'SUCCESS';
  message: string;
  result: T;
}

/**
 * Standard error response format (handled by error middleware)
 */
export interface ApiErrorResponse {
  code: string;
  message: string;
  details?: any;
}

/**
 * Success response codes
 */
export type SuccessCode = 'SUCCESS';

/**
 * Type-safe response helper that enforces the standard API response format
 * @param res Express response object
 * @param message Success message
 * @param result The data to return in the result field
 * @param statusCode HTTP status code (default: 200)
 */
export function sendResponse<T>(
  res: Response,
  message: string,
  result: T,
  statusCode: number = 200
): void {
  const response: ApiResponse<T> = {
    code: 'SUCCESS',
    message,
    result
  };
  console.log(response);

  res.status(statusCode).json(response);
}

/**
 * Convenience function for 201 Created responses
 */
export function sendCreatedResponse<T>(
  res: Response,
  message: string,
  result: T
): void {
  sendResponse(res, message, result, 201);
}

/**
 * Convenience function for 204 No Content responses
 */
export function sendNoContentResponse(res: Response): void {
  res.status(204).send();
}
