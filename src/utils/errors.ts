/**
 * @fileoverview Error handling utilities and middleware
 * Provides centralized error handling, custom error types, and error formatting
 */

import { consola } from 'consola';
import postgres from 'postgres';
import { z } from 'zod';

import type { Request, Response, NextFunction } from 'express';

/**
 * Standard HTTP error codes used throughout the application
 */
type HttpErrorCode =
  | 'BAD_REQUEST'
  | 'UNAUTHORIZED'
  | 'NOT_FOUND'
  | 'METHOD_NOT_ALLOWED'
  | 'NOT_ACCEPTABLE'
  | 'REQUEST_TIMEOUT'
  | 'CONFLICT'
  | 'GONE'
  | 'LENGTH_REQUIRED'
  | 'PRECONDITION_FAILED'
  | 'PAYLOAD_TOO_LARGE'
  | 'URI_TOO_LONG'
  | 'UNSUPPORTED_MEDIA_TYPE'
  | 'RANGE_NOT_SATISFIABLE'
  | 'EXPECTATION_FAILED'
  | 'TEAPOT';

/**
 * Application-specific error codes for backend operations
 */
type BackendErrorCode =
  | 'VALIDATION_ERROR'
  | 'USER_NOT_FOUND'
  | 'INVALID_PASSWORD'
  | 'INVALID_TOKEN'
  | 'TOKEN_EXPIRED'
  | 'DATABASE_ERROR'
  | 'UNAUTHORIZED_ACCESS';

/**
 * Union of all possible error codes in the application
 */
type ErrorCode = HttpErrorCode | BackendErrorCode | 'INTERNAL_ERROR';

/**
 * Maps error codes to HTTP status codes
 */
export function getStatusFromErrorCode(code: ErrorCode): number {
  switch (code) {
    case 'BAD_REQUEST':
    case 'VALIDATION_ERROR':
      return 400;
    case 'UNAUTHORIZED':
    case 'INVALID_PASSWORD':
    case 'INVALID_TOKEN':
    case 'TOKEN_EXPIRED':
      return 401;
    case 'NOT_FOUND':
    case 'USER_NOT_FOUND':
      return 404;
    case 'METHOD_NOT_ALLOWED':
      return 405;
    case 'NOT_ACCEPTABLE':
      return 406;
    case 'REQUEST_TIMEOUT':
      return 408;
    case 'CONFLICT':
      return 409;
    case 'GONE':
      return 410;
    case 'LENGTH_REQUIRED':
      return 411;
    case 'PRECONDITION_FAILED':
      return 412;
    case 'PAYLOAD_TOO_LARGE':
      return 413;
    case 'URI_TOO_LONG':
      return 414;
    case 'UNSUPPORTED_MEDIA_TYPE':
      return 415;
    case 'RANGE_NOT_SATISFIABLE':
      return 416;
    case 'EXPECTATION_FAILED':
      return 417;
    case 'TEAPOT':
      return 418;
    case 'DATABASE_ERROR':
    case 'INTERNAL_ERROR':
      return 500;
    default:
      return 500;
  }
}

/**
 * Gets user-friendly error messages for error codes
 */
export function getMessageFromErrorCode(code: ErrorCode): string {
  switch (code) {
    case 'BAD_REQUEST':
      return 'The request is invalid.';
    case 'VALIDATION_ERROR':
      return 'The request contains invalid or missing fields.';
    case 'UNAUTHORIZED':
    case 'UNAUTHORIZED_ACCESS':
      return 'You are not authorized to access this resource.';
    case 'INVALID_TOKEN':
      return 'The provided authentication token is invalid.';
    case 'TOKEN_EXPIRED':
      return 'The authentication token has expired.';
    case 'NOT_FOUND':
      return 'The requested resource was not found.';
    case 'USER_NOT_FOUND':
      return 'The specified user was not found.';
    case 'DATABASE_ERROR':
      return 'A database error occurred.';
    case 'INTERNAL_ERROR':
      return 'An internal server error occurred.';
    case 'CONFLICT':
      return 'The request conflicts with the current state.';
    case 'INVALID_PASSWORD':
      return 'The provided password is incorrect.';
    default:
      return 'An unexpected error occurred.';
  }
}

/**
 * Validation error detail structure
 */
export interface ValidationErrorDetail {
  path: string;
  message: string;
  code: z.ZodIssueCode;
}

/**
 * Error details structure for BackendError
 */
export interface ErrorDetails {
  message?: string;
  context?: 'params' | 'query' | 'body' | 'response';
  details?: ValidationErrorDetail[] | Record<string, unknown>;
  statusCode?: number;
}

/**
 * Formats Zod validation errors into a consistent structure
 */
export const handleValidationError = (
  error: z.ZodError
): ValidationErrorDetail[] => {
  return error.errors.map((err) => ({
    path: err.path.join('.'),
    message: err.message,
    code: err.code
  }));
};

/**
 * Custom error class for backend-specific errors with typed details
 */
export class BackendError extends Error {
  constructor(
    public code: ErrorCode,
    public details: ErrorDetails
  ) {
    super(details.message || code);
    this.name = 'BackendError';
  }
}

/**
 * Global error handling middleware
 * Processes all errors and returns standardized error responses
 */
export function errorHandler(
  error: unknown,
  req: Request,
  res: Response<{
    code: ErrorCode;
    message: string;
    details?: ValidationErrorDetail[] | Record<string, unknown>;
  }>,
  _next: NextFunction
): void {
  let statusCode = 500;
  let code: ErrorCode = 'INTERNAL_ERROR';
  let message: string;
  let details: ValidationErrorDetail[] | Record<string, unknown> | undefined;

  const ip = req.ip;
  const url = req.originalUrl;
  const method = req.method;

  if (error instanceof BackendError) {
    code = error.code;
    message = error.message;
    details = error.details.details;
    statusCode = error.details.statusCode ?? getStatusFromErrorCode(code);
  } else if (error instanceof postgres.PostgresError) {
    code = 'DATABASE_ERROR';
    message = 'A database error occurred';
    statusCode = getStatusFromErrorCode(code);
    details = {
      code: error.code,
      message: error.message,
      detail: error.detail
    };
  } else if (error instanceof z.ZodError) {
    code = 'VALIDATION_ERROR';
    message = 'Validation failed';
    details = handleValidationError(error);
    statusCode = 400;
  } else if ((error as { code: string }).code === 'ECONNREFUSED') {
    code = 'DATABASE_ERROR';
    message = 'Unable to connect to the database';
    details = {
      error: 'Connection refused',
      timestamp: new Date().toISOString()
    };
    statusCode = 500;
  } else {
    message = getMessageFromErrorCode(code);
    details = {
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    };
  }

  // Log error with context
  consola.error({
    message: `Error: ${code} - ${message}`,
    context: {
      ip,
      method,
      url,
      statusCode,
      timestamp: new Date().toISOString()
    },
    error: details
  });

  res.status(statusCode).json({
    code,
    message,
    details
  });
}

/**
 * Handles 404 Not Found errors for undefined routes
 */
export function handle404Error(_req: Request, res: Response): void {
  const code: ErrorCode = 'NOT_FOUND';
  res.status(getStatusFromErrorCode(code)).json({
    code,
    message: 'The requested endpoint does not exist'
  });
}
